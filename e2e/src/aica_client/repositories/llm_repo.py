from enum import Str<PERSON>num
from typing import Any
from langchain.chat_models import init_chat_model


class LLMModel(StrEnum):
    """
    LLMモデル名
    [e2e/src/aica_client/config.yml]の[model_list]の[model_name]と１対１
    """

    BEDROCK_CLAUDE_V1 = "bedrock-claude-v1"
    OPENAI_GPT_4_1 = "openai-gpt-4.1"


class NotSupportedProvider(Exception):
    """
    LLMModelにないモデル
    """

    def __init__(self, message):
        super().__init__(message)


class LLMRepository:
    models: dict[str, Any] = {}

    @staticmethod
    def get_or_create_model(model_name: str, model_config: dict[str, str]):
        """
        モデル初期化

        Args:
            model_name: モデル名
            model_config: モデルパラメータ

        Returns:
            モデル
        """
        if model_name not in [e.value for e in LLMModel]:
            raise NotSupportedProvider(f"サポートしないモデル: {model_name}")

        # return init_chat_model(**model_config)
        if model_name not in LLMRepository.models:
            LLMRepository.models[model_name] = init_chat_model(**model_config)

        return LLMRepository.models[model_name]
