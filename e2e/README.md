
# E2E AICA クライアント

キャリアアドバイザーテスト用のクライアント。

## 特徴

- 複数のクライアントを同時に実行
- マークダウン形式のァイルからのペルソナベースのシステムプロンプト
- 設定可能な LLM モデル（OpenAI、Bedrock）
- 会話ターン数の制限設定
- デバッグモードおよびテストモード
- パフォーマンス計測と統計収集
- 自動要約レポート生成

## 設定

`config.yml` で設定可能：

- `run_mode`: "DEBUG"（手動入力の単一クライアント）または "TEST"（自動化された複数クライアント）
- `client_number`: クライアント数（0 = すべてのペルソナを使用）
- `max_turns`: 会話のグローバルターン制限（0 = 無制限）
- `persona_included`: 使用する特定のペルソナ（オプションで max_turns を指定可）
- `persona_excluded`: 使用対象外のペルソナ
- `model_list`: 使用可能な LLM モデルとその設定

## ペルソナ

`persona/` ディレクトリにペルソナファイルを作成：

- `e2e/persona/persona_definition_01.md`
- `e2e/persona/persona_definition_02.md`
- `e2e/persona/persona_definition_03.md`
- `e2e/persona/persona_definition_04.md`
- `e2e/persona/persona_definition_05.md`
- `e2e/persona/persona_definition_06.md`
- `e2e/persona/persona_definition_07.md`
- `e2e/persona/persona_definition_08.md`
- `e2e/persona/persona_definition_09.md`
- `e2e/persona/persona_definition_10.md`

## インストール

```bash
cd e2e
pip install -e .
```

## 使い方

```bash
cd e2e
./start_test.sh
```

## パフォーマンスモニタリング

クライアントは自動で以下を記録します：
- サーバーからの初回メッセージまでの時間
- ストリーミング応答の総応答時間
- ペルソナごとの会話統計
- 全体的なパフォーマンスの平均値

テスト完了後、自動で `summary_yyyyMMddHHmmss.md` レポートを生成：
- すべての会話に関する全体統計
- ペルソナごとのパフォーマンス平均

## 環境変数

必須環境変数：
- `AICA_WS_ENDPOINT`: キャリアアドバイザーサーバーのWebsocketエンドポイント
- `AICA_API_ENDPOINT`: キャリアアドバイザーサーバーのAPIエンドポイント
- `RUN_MODE`: DEBUG または TEST
  - `DEBUG`: 設定の`CLIENT_NUMBER`と`MAX_TURNS`を無視して、1つのペルソナを使って、[こちら](https://github.com/MIIDAS-Company/miidas_aica_agent/blob/feature/e2e/first/e2e/src/aica_client/client/e2e_client.py#L219-L220)通り手動で実行をコントロールできる
  - `TEST`: 環境変数で設定の通り自動検証がは走る。
- `CLIENT_NUMBER`: クライアント数
  - 推薦値: 9
  - [参照](https://github.com/MIIDAS-Company/miidas_aica_agent/pull/16#discussion_r2191266523)
- `MAX_TURNS`: 会話ターン数の上限
  - 推薦値: 21
  - [参照](https://github.com/MIIDAS-Company/miidas_aica_agent/pull/16#discussion_r2191266523)
- `OPENAI_API_KEY`: OpenAI モデル用
- `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_SESSION_TOKEN`, `AWS_REGION_NAME`: Bedrock モデル（AWS 認証情報）用

## 残課題

求職者LLMに利用しているモデルに関して、

### Bedrock

下記2種類のエラーが発生する可能性があります

1. `An error occurred (ThrottlingException) when calling the Converse operation (reached max retries: 4): Too many requests, please wait before trying again.`

[ここ](https://ap-northeast-1.console.aws.amazon.com/servicequotas/home/<USER>/bedrock/quotas)から見ると、`Not adjustable`なので、backoff（現在10回リトライ）応していますが、完全防止できない。

2. `An error occurred (ThrottlingException) when calling the Converse operation (reached max retries: 4): Too many tokens, please wait before trying again.`

[ここ](https://ap-northeast-1.console.aws.amazon.com/servicequotas/home/<USER>/bedrock/quotas)から見ると、デフォルトのon demandモデルは、Quotaの調整できなさそうなので、現現たぶん発生するたびに、古い会話履歴からけすしかない。まだやってない。

3. 原因不明だが、会話回数が増えるたびに、実行時間が長くなります。
