[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "AICA-Client"
version = "0.0.1"
description = "AICAエージェントテストのE2Eクライアント"
readme = "README.md"
requires-python = "=3.12"
classifiers = [
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "langgraph",
    "langchain",
    "langchain-openai",
    "langchain-aws",
    "websockets",
    "pydantic",
    "PyYAML",
    "aiohttp",
    "backoff",
]

[tool.hatch.build.targets.wheel]
packages = ["aica_client"]