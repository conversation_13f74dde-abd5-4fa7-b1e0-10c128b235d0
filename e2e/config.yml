agent_server:
  ws_url: ${AICA_WS_ENDPOINT}
  api_url: ${AICA_API_ENDPOINT}
model_list:
  - model_name: openai-gpt-4.1
    disabled: true
    model_settings:
      model: openai:gpt-4.1
      temperature: 1.0
      top_p: 0.999
  - model_name: bedrock-claude-v1
    disabled: false
    model_settings:
      model: bedrock_converse:anthropic.claude-3-5-sonnet-20240620-v1:0
      temperature: 1.0
      top_p: 0.999
      aws_access_key_id: ${AWS_ACCESS_KEY_ID}
      aws_secret_access_key: ${AWS_SECRET_ACCESS_KEY}
      aws_session_token: ${AWS_SESSION_TOKEN}
      region_name: ${AWS_REGION_NAME}
persona_location: persona
# persona_included:
#   - name: aaa
#     max_turns: 50
#   - name: bbb
# persona_excluded:
#   - name: ccc
#   - name: ddd
run_mode: ${RUN_MODE}
client_number: ${CLIENT_NUMBER}
# max_turns<=0の場合、無限ループ
max_turns: ${MAX_TURNS}
