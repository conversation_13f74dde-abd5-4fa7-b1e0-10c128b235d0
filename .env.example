OPENAI_API_KEY=
AICA_AGENT_PORT=
AICA_AGENT_DB_HOST=
AICA_AGENT_DB_PORT=
AICA_AGENT_DB_USER=
AICA_AGENT_DB_PASSWORD=
AICA_AGENT_DB_NAME=
AICA_AGENT_DB_SSLMODE=
AICA_AGENT_MCP_ENDPOINT=
AICA_AGENT_API_ENDPOINT=

# bedrockを利用するときのみ
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=
AWS_REGION_NAME=

# disable OpenAI Agents tracing
OPENAI_AGENTS_DISABLE_TRACING=1
# disable LiteLL<PERSON> debug log
OPENAI_AGENTS_DONT_LOG_MODEL_DATA=1
