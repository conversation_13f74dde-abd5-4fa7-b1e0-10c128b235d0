#!/bin/bash

DB_HOST=""
DB_PORT=""
DB_USER=""
DB_PASSWORD=""
DB_NAME=""

export PGPASSWORD=$DB_PASSWORD

# Function: Check if a table exists in the public schema
check_table() {
  local table=$1
  psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');"
}

# Function: Check if the function update_timestamp_column() exists in the public schema
check_function() {
  psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE p.proname = 'update_timestamp_column' AND n.nspname = 'public');"
}

# Function: Run a SQL file on the remote database
run_sql_file() {
  local sql_file=$1
  if [ -f "$sql_file" ]; then
    echo "Executing SQL file: $sql_file"
    cat "$sql_file" | psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME"
    if [ $? -ne 0 ]; then
      echo "Error executing $sql_file"
      exit 1
    fi
  else
    echo "SQL file $sql_file not found! Skipping..."
  fi
}

# Function: Ensure that a table exists. If not, check and create the function update_timestamp_column(),
# and then run the provided SQL file to create/import the table.
ensure_table_exists() {
  local table_name="$1"
  local sql_file="$2"

  echo "Checking for table public.${table_name}..."
  local table_exists=$(check_table "${table_name}")
  echo "Table public.${table_name} exists: ${table_exists}"
  if [ "${table_exists}" != "t" ]; then
    echo "Table public.${table_name} does not exist."
    local func_exists=$(check_function)
    echo "Function public.update_timestamp_column exists: ${func_exists}"
    if [ "${func_exists}" != "t" ]; then
      echo "Function public.update_timestamp_column() does not exist. Creating it..."
      run_sql_file "db_migrations/sql/001_trigger.sql"
    fi
    echo "Creating and importing table public.${table_name} using ${sql_file}..."
    run_sql_file "db_migrations/sql/${sql_file}"
    run_sql_file "db_migrations/data/${sql_file}"
  else
    echo "Table public.${table_name} already exists. Skipping creation."
  fi
}

# Main script logic: Dynamically process all SQL files in db_migrations/sql
for sql_file in db_migrations/sql/*.sql; do
  # Extract the table name by removing the numeric prefix and `.sql` extension
  table_name=$(basename "$sql_file" .sql | sed 's/^[0-9]*_//')
  
  # Ensure the table exists and process the SQL file
  ensure_table_exists "$table_name" "$(basename "$sql_file")"
done

echo "Database checks and updates completed."