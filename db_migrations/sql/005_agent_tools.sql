CREATE TABLE IF NOT EXISTS public.agent_tools
(
    id bigserial NOT NULL,
    agent_id bigint NOT NULL,
    tool_name character varying(255) NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    CONSTRAINT agent_tools_pkey PRIMARY KEY (id),
    CONSTRAINT agent_tool_agent_id_tool_name UNIQUE (agent_id, tool_name)
);


CREATE OR REPLACE TRIGGER update_timestamp_trigger
    BEFORE UPDATE 
    ON public.agent_tools
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
