CREATE TABLE IF NOT EXISTS public.chat_sessions
(
    id bigserial NOT NULL,
    session_id character varying(255) NOT NULL,
    secret_key character varying(255) NOT NULL,
    summary text,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    CONSTRAINT chat_sessions_pkey PRIMARY KEY (id),
    CONSTRAINT chat_sessions_session_id_unique UNIQUE (session_id)
);

CREATE OR REPLACE TRIGGER update_timestamp_trigger
    BEFORE UPDATE 
    ON public.chat_sessions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
