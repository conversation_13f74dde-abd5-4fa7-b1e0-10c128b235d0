CREATE TABLE IF NOT EXISTS public.user_profiles
(
    id bigserial NOT NULL,
    session_id character varying(255) NOT NULL,
    user_name character(255),
    user_purpose text,
    interest_tendency text,
    job_search_motivation text,
    current_job_experience_years integer,
    current_job_description text,
    job_search_filter json,
    job_feedback_positive text[],
    job_feedback_negative text[],
    created_at timestamp without time zone,
    CONSTRAINT user_profiles_pkey PRIMARY KEY (id),
    CONSTRAINT user_profiles_session_id_unique UNIQUE (session_id),
    CONSTRAINT user_profiles_session_id_fkey
        FOREIGN KEY (session_id)
        REFERENCES public.chat_sessions(session_id)
        ON DELETE CASCADE
)
