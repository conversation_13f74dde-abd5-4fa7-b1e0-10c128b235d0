CREATE TABLE IF NOT EXISTS public.agents
(
    id bigserial NOT NULL,
    name character varying(256) NOT NULL,
    description text NOT NULL,
    default_agent boolean NOT NULL DEFAULT false,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    CONSTRAINT agents_pkey PRIMARY KEY (id),
    CONSTRAINT agents_unique_name UNIQUE (name)
);


CREATE OR REPLACE TRIGGER update_timestamp_trigger
    BEFORE UPDATE 
    ON public.agents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
