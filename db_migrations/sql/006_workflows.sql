CREATE TABLE IF NOT EXISTS public.workflows
(
    id bigserial NOT NULL,
    src_agent_id bigint NOT NULL,
    dest_agent_id bigint NOT NULL,
    description text,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    CONSTRAINT workflows_pkey PRIMARY KEY (id),
    CONSTRAINT workflows_unique_src_dest UNIQUE (src_agent_id, dest_agent_id)
);


CREATE OR REPLACE TRIGGER update_timestamp_trigger
    BEFORE UPDATE 
    ON public.workflows
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
