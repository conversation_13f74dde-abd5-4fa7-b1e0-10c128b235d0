--
-- PostgreSQL database dump
--

-- Dumped from database version 16.4 (Debian 16.4-1.pgdg120+2)
-- Dumped by pg_dump version 17.0

-- Started on 2025-06-14 12:25:23 JST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
-- SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 3645 (class 0 OID 79107)
-- Dependencies: 236
-- Data for Name: workflows; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.workflows (id, src_agent_id, dest_agent_id, description, created_at, updated_at, deleted_at) VALUES (1, 1, 2, NULL, '2025-04-22 08:53:56.969315', NULL, NULL);


--
-- TOC entry 3651 (class 0 OID 0)
-- Dependencies: 235
-- Name: workflows_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.workflows_id_seq', 3, true);


-- Completed on 2025-06-14 12:25:23 JST

--
-- PostgreSQL database dump complete
--

