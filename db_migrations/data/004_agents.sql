--
-- PostgreSQL database dump
--

-- Dumped from database version 16.4 (Debian 16.4-1.pgdg120+2)
-- Dumped by pg_dump version 17.0

-- Started on 2025-06-14 00:34:35 JST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
-- SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 3645 (class 0 OID 79060)
-- Dependencies: 232
-- Data for Name: agents; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.agents (id, name, description, default_agent, created_at, updated_at, deleted_at) VALUES (1, 'DefaultAgent', 'デフォルトエージェント', true, '2025-04-15 16:43:23.572596', '2025-04-16 04:27:55.474582', NULL);
INSERT INTO public.agents (id, name, description, default_agent, created_at, updated_at, deleted_at) VALUES (2, 'CareerAdvisor', 'キャリア相談エージェント', false, '2025-04-15 16:43:23.572596', '2025-04-16 04:27:55.474582', NULL);
INSERT INTO public.agents (id, name, description, default_agent, created_at, updated_at, deleted_at) VALUES (3, 'PositionGuide', 'ポジション詳細説明エージェント', false, '2025-05-29 09:32:09.676897', NULL, NULL);


--
-- TOC entry 3651 (class 0 OID 0)
-- Dependencies: 231
-- Name: agents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.agents_id_seq', 3, true);


-- Completed on 2025-06-14 00:34:35 JST

--
-- PostgreSQL database dump complete
--

