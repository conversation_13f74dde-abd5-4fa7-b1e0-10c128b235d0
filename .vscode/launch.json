{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Agent",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "cwd": "${workspaceFolder}/src/aica_agent",
            "args": [
                "application:app",
                "--reload",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--limit-concurrency", "100",
                "--timeout-keep-alive", "30"
            ],
            "envFile": "${workspaceFolder}/.env.local",
        },
        {
            "name": "Agent (Production-like)",
            "type": "debugpy",
            "request": "launch",
            "module": "gunicorn",
            "cwd": "${workspaceFolder}/src/aica_agent",
            "args": [
                "-k", "uvicorn.workers.UvicornWorker",
                "--worker-connections", "100",
                "--max-requests", "100",
                "--max-requests-jitter", "10",
                "--timeout", "300",
                "--bind", "0.0.0.0:8000",
                "application:app"
            ],
            "envFile": "${workspaceFolder}/.env.local",
            "env": {
                "OBJC_DISABLE_INITIALIZE_FORK_SAFETY": "YES"
            }
        },
        {
            "name": "AICA Client",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/e2e/src/aica_client/main.py",
            "cwd": "${workspaceFolder}/e2e",
            "envFile": "${workspaceFolder}/e2e/.env.local"
        },
        {
            "name": "AICA Client (Dev)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/e2e/src/aica_client/main.py",
            "cwd": "${workspaceFolder}/e2e",
            "envFile": "${workspaceFolder}/e2e/.env.dev"
        },
        {
            "name": "AICA Batch (Clean Session)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/cli/src/aica_batch/main.py",
            "args": ["clean_session"],
            "cwd": "${workspaceFolder}/cli",
            "envFile": "${workspaceFolder}/cli/.env.local"
        }
    ]
}
