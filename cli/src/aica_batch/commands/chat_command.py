from contextlib import AbstractContextManager
import logging
from sqlalchemy.orm import Session
from typing import Callable

from ..const import LOGGER_PREFIX


class ChatCommand:
    """
    チャット操作関連コマンド
    """

    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        """
        インスタンス初期化

        Args:
            session_factory: DBセッションッ
        """
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )
        self._session_factory = session_factory

    def clean_session(self):
        self._logger.debug("clean session")