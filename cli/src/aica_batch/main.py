from dependency_injector.wiring import inject
import typer

from containers import Container

app = typer.Typer(help="AICA Batch CLI")
container = Container()

@app.command("clean_session", help="セッションをクリーニングする")
@inject
def clean_session():
    chat_command = container.chat_command_factory()
    chat_command.clean_session()


@app.command(hidden=True)
def _dummy():
    """
    コマンドが1つしかない場合、実行時にコマンド名を指定できないので、ダミーを作った
    """
    pass

if __name__ == "__main__":
    app()
