[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "AICA-Batch"
version = "0.0.1"
description = "AICAエージェントバッチ"
readme = "README.md"
requires-python = "=3.12"
classifiers = [
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
]
dependencies = [
    "psycopg[binary]==3.2.6",
    "SQLAlchemy==2.0.40",
    "typer",
]

[project.urls]
Repository = "https://github.com/MIIDAS-Company/miidas_aica_agent"
Issues = "https://github.com/MIIDAS-Company/miidas_aica_agent/issues"

[tool.hatch.build.targets.wheel]
packages = ["aica_batch"]

[tool.hatch.metadata]
allow-direct-references = true
