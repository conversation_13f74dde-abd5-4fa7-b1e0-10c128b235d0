from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from containers import Container
import endpoints


container = Container()


@asynccontextmanager
async def lifespan(_: FastAPI):
    await container.init_resources()
    yield
    await container.shutdown_resources()


app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

app.container = container
app.include_router(endpoints.router)
