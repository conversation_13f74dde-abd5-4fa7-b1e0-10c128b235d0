import copy
from enum import StrEnum
import json
import time
from typing import Any, AsyncGenerator
from typing import Tuple
import uuid
from datetime import datetime

from openai.types.responses import ResponseTextDeltaEvent, ResponseOutputTextParam
from openai.types.responses.response_input_item_param import Message
from openai.types.responses.response_input_text_param import ResponseInputTextParam
from agents import (
    Agent,
    HandoffCallItem,
    HandoffOutputItem,
    MessageOutputItem,
    ReasoningItem,
    RunItem,
    Runner,
    TResponseInputItem,
    ToolCallItem,
    ToolCallOutputItem,
    RunResultStreaming,
)

from domain.enitites.chat_history import ChatHistory
from repositories.llm_repo import AgentName, LLMRepository
from repositories.position_repo import PositionRepository
from repositories.chat_repo import ChatRepository
from repositories.user_repo import UserRepository
from services.base_service import BaseService
from utils.chat_response import ChatStreamReponse

DEFAULT_ERROR_MESSAGE = (
    "大変混み合っておりますので、しばらく経ってからリロードしてご利用ください。"
)
POSITION_SEARCH_FAKE_RESULT = "ポジション検索が実行されました。ユーザーには別の手段で求人の検索結果を見せていますが、ユーザーから条件変更や再度見たいとの要望があれば、検索条件の差異に関わらず、再度このツールを実行してください。"

MAIN_CHAT_KEY = "MAIN"

POSITION_DETAIL_INQUIRY_START_PROMPT = """指定ポジションは下記となります。ユーザーに求人情報について回答する時に、下記の内容を確認してください。

#求人情報
%s
#会社詳細情報
%s
#事業詳細情報
%s
#パラメータの補足解説\n下記は、各情報について、*どのパラメータを確認すると見つけかりやすいか*を補足解説しています。
【必須】

##求人情報についての補足
どのような人が歓迎されるか: HREvaluationCompetency
どのような所がこの会社の魅力か: PR
会社名: Company要素内のName

##会社詳細情報についての補足
会社名: Prefectureの前にあるNameを確認してください。
何をしている会社か: Introduction,PR
特別な評価制度はあるか: HREvaluationSpecialSystem
福利厚生はどうか: Welfare,WelfareOther

##事業詳細についての補足
この会社の業界(業種)での立ち位置がわかります。
有形商材か無形商材か: Tangibleness"""

POSITION_DETAIL_INQUIRY_SUMMARY_PROMPT = """#あなたは今回の求人でユーザー会話した内容の要約してください。
要約に必要な情報:
何についてユーザーから質問されたか
回答できていた場合、その回答の求人情報の該当箇所の抜粋
事実のみを簡潔に記述。勝手な創作は推定は排除
#デバッグ用に下記のパターンに合わせた文章を出力してください
*会話の要約*には、以下を含めるようにしましょう
{{ユーザーが気にしていた情報と、それについてあなたが答えた企業情報}}
{{ユーザーが気にしていたが、あなたが答えられなかった企業情報}}
*パターンA*: *応募に誘導できる条件*に該当し、応募してみたい旨のメッセージを受け取っている場合
例:「求人情報エージェントからの引き継ぎ: ユーザーは{{企業名}}に興味を持っています。応募の案内をお願いします。会話の要約は下記です。
{{*会話の要約*}}」
*パターンB*: *応募に誘導できる条件*に該当し、特に何も言われていない場合
例:「求人情報エージェントからの引き継ぎ: ユーザーは{{企業名}}について、求人情報エージェントと下記の会話を行いましたが、応募の案内を行う必要はありません。
{{*会話の要約*}}」
*パターンC*: *応募に誘導できる条件*に該当していない場合
「求人情報エージェントからの引き継ぎ: ユーザーは{{企業名}}の求人情報を閲覧しました。この企業に対しての積極的な興味は観測できませんでした。」"""


class LLMMessageRole(StrEnum):
    DEVELOPER = "developer"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"
    HANDOFF = "handoff"
    REASONING = "reasoning"
    SYSTEM = "system"


class MessageType(StrEnum):
    MESSAGE = "message"
    POSITION_SEARCH_RESULT = "position_search_result"
    ERROR = "error"
    END = "end"


class PageName(StrEnum):
    CHAT = "Chat"
    POSITION_DETAIL = "PositionDetail"


class ChatService(BaseService):
    """
    LLM会話サービス
    """

    def __init__(
        self,
        llm_repository: LLMRepository,
        chat_repository: ChatRepository,
        position_repository: PositionRepository,
        user_repository: UserRepository,
    ) -> None:
        """
        インスタンス初期化

        Args:
            llm_repository: LLMモデルリポジトリ
            chat_repository: 会話履歴リポジトリ
            position_repository: ポジションデータリポジトリ
        """
        super().__init__()

        # LLMプロバイダー（OpenAI、Bedrock）
        self._provider: str | None = None
        self._llm_repository = llm_repository
        self._chat_repository = chat_repository
        self._position_repository = position_repository
        self._user_repository = user_repository
        self._agents: dict[str, Agent] = {}
        self._position_agents: dict[int, Agent] = {}
        # 今会話しているエージェント
        self._active_agent: Agent = None
        # セッション作成済みフラグ。※挨拶だけの会話は保存しないので、このフラグを設けています。
        # True：chat_sessionデータがすでに作られている
        # False：新しいセッションなので、まだchat_sessionデータが作られていない。
        self._session_created = False
        # DB保存フラグ。※挨拶だけの会話は保存しないので、このフラグを設けています。
        # LLMとの会話は1回だけの場合、挨拶扱いとしてDBデータは保存しない
        # 2回目から、初めての挨拶を含めてDBにデータを保存します。
        # True：LLMとの会話は2回以上の場合、保存すべき
        # False：まだ挨拶だけなので、DB保存はしない
        self._should_save = False
        # 会話履歴
        self._conversation_history: dict[str | int, list] = {}
        self._chat_key: str | int = MAIN_CHAT_KEY
        self._position_id: int | None = None

    async def init_session(
        self,
        session_id: str,
        provider: str,
    ) -> str:
        """
        セッション初期化。※セッションはwebsocket接続毎に１つ

        Args:
            session_id: セッションID
            provider: LLMプロバイダー
        """
        self._session_id = session_id
        self._provider = provider
        agents = self._llm_repository.clone_agents(self._provider)
        for agent_name, (agent, default_agent) in agents.items():
            self._agents[agent_name] = agent
            if default_agent:
                self._active_agent = agent
        self._toolcall_trace_message = {
            "type": "message",
            "role": LLMMessageRole.DEVELOPER,
            "content": f"""### ツール呼び出すときのパラメータについて
セッションID：{self._session_id}を利用してください。
リクエストID：なるべく重複しないよう任意のuuidを生成して、リクエストごとにユニークな値を設定してください。
""",
        }

        try:
            (chat_session, exists) = self._chat_repository.init_chat_session(
                self._session_id,
            )
            if chat_session:
                self._position_repository.save_session_key(
                    self._session_id,
                    chat_session.secret_key,
                )

                # DBにセッションデータがあるので、セッション作成済みフラグをTrueにする。
                self._session_created = True
                if chat_session.histories:
                    # 既存セッションの再開なので、前回のアクティブエージェントを続いて利用する。
                    active_agent_name = chat_session.histories[-1].active_agent
                    self._active_agent = self._get_agent(active_agent_name)
                    # 以前の会話から続くため、会話履歴をロードして、LLMに渡す。
                    self._conversation_history = self._convert_to_llm_messages(
                        chat_session.histories
                    )
                    # DB保存済みセッションの再開なので、つまりLLMと2回以上会話しているので、DB保存フラグをTrueにする。
                    self._should_save = True

                if chat_session.user_profile:
                    self._position_repository.save_user_preference(
                        self._session_id,
                        chat_session.user_profile.job_search_filter,
                    )
            elif exists:
                self._session_id = str(uuid.uuid4())

            if (
                MAIN_CHAT_KEY not in self._conversation_history
                or not self._conversation_history.get(MAIN_CHAT_KEY)
            ):
                self._conversation_history[MAIN_CHAT_KEY] = [
                    self._toolcall_trace_message
                ]

            return self._session_id
        except Exception as e:
            self.logger.exception(f"セッション初期化失敗: {e}")
            return ""

    def _convert_to_llm_messages(
        self,
        histories: list[ChatHistory],
    ) -> dict[str | int, list]:
        """
        DB履歴でデータからLLM会話履歴作成。

        Args:
            histories: DB履歴でデータ
        """
        all_messages: dict[str | int, list] = {}
        for history in histories:
            history_key = history.position_id if history.position_id else MAIN_CHAT_KEY
            messages = all_messages.setdefault(history_key, [])

            if history.role in [LLMMessageRole.USER, LLMMessageRole.DEVELOPER]:
                messages.append(
                    {
                        "type": "message",
                        "role": history.role,
                        "content": history.content,
                    }
                )
            elif history.role == LLMMessageRole.ASSISTANT:
                messages.append(
                    {
                        "type": "message",
                        "role": history.role,
                        "content": [
                            ResponseOutputTextParam(
                                type="output_text",
                                text=history.content,
                            )
                        ],
                    }
                )
            elif history.role in [LLMMessageRole.TOOL, LLMMessageRole.HANDOFF]:
                messages.append(
                    {
                        "type": "function_call",
                        "call_id": history.tool_call_id,
                        "name": history.tool_name,
                        "arguments": json.dumps(history.tool_input),
                    }
                )

                output = history.content
                agent = self._get_agent(history.active_agent)
                if isinstance(agent.tool_use_behavior, dict):
                    names = agent.tool_use_behavior.get("stop_at_tool_names", [])
                    if history.tool_name in names:
                        # return_redirect=Trueのツールの場合、実際の実行結果ではなく、フェイク結果をLLMに渡す
                        output = POSITION_SEARCH_FAKE_RESULT

                messages.append(
                    {
                        "type": "function_call_output",
                        "call_id": history.tool_call_id,
                        "output": output,
                    }
                )
            elif history.role == LLMMessageRole.REASONING:
                pass
            else:
                self.logger.error(f"Unsupported message role: {history}")
        return all_messages

    async def chat(
        self,
        input: dict,
    ) -> AsyncGenerator[str, None]:
        """
        ユーザーインプットをLLMに渡して、レスポンスを返す。
        インプットごとに呼び出される

        Args:
            message: ユーザーインプット

        Returns:
            LLMレスポンス
        """
        self.logger.debug(f"Chat: {input}")

        encrypted_position_id = input.get("position_id")
        message = input["message"]

        try:
            if encrypted_position_id:
                position_summary = self._position_repository.get_position_summary(
                    self._session_id,
                    encrypted_position_id,
                )
                self._position_id = (
                    position_summary.get("ID") if position_summary else None
                )
                if not self._position_id:
                    # IMPOSSIBLE：セッションタイムアウトの場合発生しうるが、セッションタイムアウトの場合、そもそもポジション詳細に入れないなので、ここまでこれないはず
                    raise ValueError(
                        f"ポジションIDが見つかりません: {encrypted_position_id}"
                    )
                self._chat_key = self._position_id
            else:
                self._position_id = None
                self._chat_key = MAIN_CHAT_KEY

            prepare_result = await self._prepare_for_chat_turn(input)
            if prepare_result:
                yield prepare_result
                return

            self._conversation_history[self._chat_key].append(
                Message(
                    type="message",
                    role=LLMMessageRole.USER,
                    content=[ResponseInputTextParam(type="input_text", text=message)],
                ),
            )

            result = None
            while True:
                # ポジション検索ツールからバリデーションエラーを返してくれているかどうか
                position_search_validation_error = False
                # return_directツール情報
                stop_at_tool_calls: dict[str, ChatHistory] = {}
                # ユーザー会話からもらったポジション検索条件です。おすすめリンクに利用されますｍ2。
                # 2種類のツールから収集
                # ・save_xxx_preferenceツール
                # ・ポジション検索ツール（return_directツール）
                # 　※ 最初save_系ツールだけから収集してたが、ポジション検索ツールが先に呼ばれる可能性があります。
                #     ポジション検索条件はポジション検索結果のおすすめリンクは検索条件がないとエラーになりますので、ポジション検索ツールからも収集します。
                user_preferences_save_tool_calls: list[str] = []

                chat_response = ChatStreamReponse(
                    self._session_id,
                    encrypted_position_id,
                    str(uuid.uuid4()),
                )

                start_time = time.time()
                run_result = Runner.run_streamed(
                    self._active_agent,
                    input=self._conversation_history[self._chat_key],
                )

                async for event in run_result.stream_events():
                    if event.type == "raw_response_event":
                        if isinstance(event.data, ResponseTextDeltaEvent):
                            # Streaming response from LLM
                            yield chat_response.create_message_response(
                                event.data.delta
                            )
                    elif event.type == "run_item_stream_event":
                        if event.item.type == "tool_call_item":
                            # Tool call
                            self._handle_tool_call_item(
                                event.item,
                                user_preferences_save_tool_calls,
                                stop_at_tool_calls,
                            )
                        elif event.item.type == "tool_call_output_item":
                            # Tool call result
                            parsed_output = self._parse_tool_output(
                                event.item.raw_item["output"]
                            )

                            # ツール実行失敗しているかを判断する
                            # 結果がないか、"Message"キーが入っている場合、失敗とみなす
                            if "Message" in parsed_output:
                                # ツール実行失敗
                                self.logger.error(f"ツール実行失敗: {event.item}")
                                position_search_validation_error = (
                                    event.item.raw_item["call_id"] in stop_at_tool_calls
                                )
                                break

                            if (
                                event.item.raw_item["call_id"]
                                in user_preferences_save_tool_calls
                            ):
                                parsed_output.pop("MessageToLLM", None)
                                self._position_repository.save_user_preference(
                                    self._session_id,
                                    parsed_output,
                                )
                                self._user_repository.update_job_search_filter(
                                    self._session_id,
                                    parsed_output,
                                )
                            elif event.item.raw_item["call_id"] in stop_at_tool_calls:
                                stop_at_tool_calls[
                                    event.item.raw_item["call_id"]
                                ].content = event.item.raw_item["output"]
                                yield self._process_position_search_result(
                                    parsed_output,
                                )
                end_time = time.time()
                elapsed = end_time - start_time
                self.logger.info(
                    f"chat_service.py: chat turn took {elapsed:.2f} seconds."
                )

                result = run_result
                self._save_chat(message, result)

                if position_search_validation_error:
                    # ポジション詳細お問い合わせには、ポジション検索ツールが呼ばれないので、chat_keyは常にMAIN_CHAT_KEYのはず
                    self._conversation_history[self._chat_key] = (
                        run_result.to_input_list()
                    )
                else:
                    break
        except Exception as e:
            self.logger.exception(e)
            yield ChatStreamReponse(
                self._session_id,
                encrypted_position_id,
            ).create_error_response(DEFAULT_ERROR_MESSAGE)
            return

        if result:
            self._update_conversation_history(result, stop_at_tool_calls)

        yield ChatStreamReponse(
            self._session_id,
            encrypted_position_id,
        ).create_end_response()

    async def _prepare_for_chat_turn(self, input: dict) -> ChatStreamReponse | None:
        """
        フロントからのINPUT解析

        Args:
            input: フロントからのINPUT
        """
        current_page = input.get("current_page")
        encrypted_position_id = input.get("position_id")
        message = input["message"]

        if current_page == PageName.CHAT:
            # `/chat` 初めてアクセス
            # OR
            # if prev_page == PageName.POSITION_DETAIL:
            # `/positions/{position_id}` => `/chat`
            # ユーザが連続でポジション詳細を開く可能性がありますので、ポジポジシ詳細お問い合わせ後メインチャットからのユーザーメががッセが来てからサマルなら、もれがあるので、
            # メインチャットに戻ったたびにポジション詳細お問い合わせがもしあればLLMにサマってもらって_conversation_historyに追加する。
            if message == "###PositionAdvisorEnd###":
                if self._conversation_history.get(self._chat_key):
                    # ポジション詳細お問い合わせがある場合のみ
                    # サマルプロンプト自体は会話履歴に入れない
                    position_detail_inquiries = copy.deepcopy(
                        self._conversation_history[self._chat_key]
                    )
                    position_detail_inquiries.append(
                        Message(
                            type="message",
                            role=LLMMessageRole.DEVELOPER,
                            content=[
                                ResponseInputTextParam(
                                    type="input_text",
                                    text=POSITION_DETAIL_INQUIRY_SUMMARY_PROMPT,
                                )
                            ],
                        ),
                    )
                    # いまはPOSITION_GUIDEのはず
                    result = await Runner.run(
                        self._active_agent,
                        input=position_detail_inquiries,
                    )
                    # LLMから返答をもらった後DBに保存するのは、ユーザーからのメッセージとLLMからの返答のみ、
                    # また、ポジション詳細からメインチャットに戻った後、ユーザーはもうメッセージを送ってくれない可能性もあります。
                    # なので、いまサマリをDBに保存するしかない
                    self._chat_repository.add_chat_history(
                        ChatHistory(
                            session_id=self._session_id,
                            position_id=None,
                            active_agent=AgentName.POSITION_GUIDE,
                            message_id=None,
                            role=LLMMessageRole.ASSISTANT,
                            content=result.final_output,
                        )
                    )

                    # ポジション詳細から戻ってきた時点では、LLMからの返答が必要ないので、いますぐさまった情報をLLMに送らず、メインチャット会話履歴を入れたら終わり
                    # 次のメインチャットでのユーザーからのメッセージが来たら、一緒にLLMに送る。
                    self._conversation_history[MAIN_CHAT_KEY].append(
                        {
                            "type": "message",
                            "role": LLMMessageRole.ASSISTANT,
                            "content": [
                                ResponseOutputTextParam(
                                    type="output_text",
                                    text=result.final_output,
                                )
                            ],
                        }
                    )
                    return ChatStreamReponse(
                        self._session_id,
                        encrypted_position_id,
                    ).create_end_response()

            if self._active_agent.name == AgentName.POSITION_GUIDE:
                self._active_agent = self._get_agent(AgentName.CAREER_ADVISOR)
        elif current_page == PageName.POSITION_DETAIL and encrypted_position_id:
            # `/chat` => `/positions/{position_id}`
            if self._active_agent.name != AgentName.POSITION_GUIDE:
                self._active_agent, _ = self._get_or_create_position_agent(
                    self._position_id
                )

            if not self._conversation_history.get(self._chat_key):
                position_detail, company_detail, business_detail, error_message = (
                    self._get_position_detail(encrypted_position_id)
                )

                if error_message:
                    self.logger.error(error_message)
                    raise ValueError(error_message)

                # 当初の想定ではポジション詳細をLLMに伝えるにはシステムプロンプトを利用できるようになるのはポジション詳細Agentクローンのメリットの1つかと思ってましたが、
                # 中村さんと相談（https://miidas-dev.slack.com/archives/C08BU50QS3Y/p1751329093253549?thread_ts=1750744115.699779&cid=C08BU50QS3Y）して、
                # 「求人詳細については一旦今のまま(developerロールでのメッセージ扱い)が安定しそうです！」なので、システムプロンプトの利用はをやめました。
                message = POSITION_DETAIL_INQUIRY_START_PROMPT % (
                    json.dumps(position_detail),
                    json.dumps(company_detail),
                    json.dumps(business_detail),
                )

                self._conversation_history[self._chat_key] = [
                    self._toolcall_trace_message,
                    Message(
                        type="message",
                        role=LLMMessageRole.DEVELOPER,
                        content=[
                            ResponseInputTextParam(type="input_text", text=message)
                        ],
                    ),
                ]
                # LLMから返答をもらった後DBに保存するのは、ユーザーからのメッセージとLLMからの返答のみ、
                # なので、いまサマリをDBに保存するしかない
                self._chat_repository.add_chat_histories(
                    [
                        ChatHistory(
                            session_id=self._session_id,
                            position_id=self._position_id,
                            active_agent=AgentName.POSITION_GUIDE,
                            message_id=None,
                            role=self._toolcall_trace_message["role"],
                            content=self._toolcall_trace_message["content"],
                        ),
                        ChatHistory(
                            session_id=self._session_id,
                            position_id=self._position_id,
                            active_agent=AgentName.POSITION_GUIDE,
                            message_id=None,
                            role=LLMMessageRole.DEVELOPER,
                            content=message,
                        ),
                    ]
                )
        else:
            error_msg = f"Unknown page: {current_page}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

    def _parse_tool_output(self, output: str) -> dict:
        """
        ツール結果解析

        Args:
            output: 生のツール結果

        Returns:
            解析後のツール結果
        """
        self.logger.debug(f"ツールのoutput: {output}")
        outer_result = json.loads(output)
        inner_result_str = outer_result.get("text")
        if not isinstance(inner_result_str, str):
            raise Exception(
                f"ツールのoutputの'text'フィールドは文字列ではない: {output}"
            )
        return json.loads(inner_result_str)

    def _handle_tool_call_item(
        self,
        item: ToolCallItem,
        user_preferences_save_tool_calls: list[str],
        stop_at_tool_calls: dict[str, ChatHistory],
    ):
        """
        ポジション検索ツール、ポジション検索条件保存ツールの入力から検索条件を保存する

        Args:
            item: ツールコール入力
            user_preferences_save_tool_calls: 該当会話呼ばれたポジション検索条件保存ツール
            stop_at_tool_calls: 該当会話呼ばれたreturn_directツール

        Returns:
            失敗の場合: True
            成功の場合: False
        """
        if item.raw_item.name.startswith("save_") and item.raw_item.name.endswith(
            "_preference"
        ):
            # Tools named with [save_xxx_preference] is to save position search conditions of a user
            user_preferences_save_tool_calls.append(item.raw_item.call_id)
            return

        agent = item.agent
        if isinstance(agent.tool_use_behavior, dict):
            # return_direct tools. Currently only position search tool is return_direct.
            names = agent.tool_use_behavior.get("stop_at_tool_names", [])
            if item.raw_item.name in names:
                try:
                    tool_input = json.loads(item.raw_item.arguments)
                except (json.JSONDecodeError, TypeError) as e:
                    self.logger.exception(
                        f"ポジション検索条件（ツールパラメータ）解析失敗しました: {e}"
                    )
                    return

                # Populate stop_at_tool_calls first.
                stop_at_tool_calls[item.raw_item.call_id] = ChatHistory(
                    session_id=self._session_id,
                    position_id=self._position_id,
                    active_agent=agent.name,
                    message_id=item.raw_item.id,
                    role=LLMMessageRole.TOOL,
                    tool_call_id=item.raw_item.call_id,
                    tool_name=item.raw_item.name,
                    tool_input=tool_input,
                )

                # Then, save preferences.
                preference_input = tool_input.copy()
                preference_input.pop("SessionID", None)
                preference_input.pop("RequestID", None)
                self._position_repository.save_user_preference(
                    self._session_id,
                    preference_input,
                )
                self._user_repository.update_job_search_filter(
                    self._session_id,
                    preference_input,
                )

    def _process_position_search_result(
        self,
        tool_result: dict[str, Any] | None,
    ) -> ChatStreamReponse:
        """
        ポジション検索ツール結果分析し、結果をLLMに渡さず、直接フロントに返す。

        Args:
            tool_result: ポジション検索結果

        Returns:
            ポジション検索結果レスポンス
        """
        search_key = str(int(datetime.now().timestamp()))
        count = self._position_repository.save_search_result_position_ids(
            self._session_id,
            search_key,
            tool_result.get("AllPositionIds"),
        )

        position_summaries = self._position_repository.process_and_cache_positions(
            self._session_id, tool_result.get("Positions")
        )

        recommendations = tool_result.get("Recommendations", [])
        if recommendations:
            theme_mapping = self._position_repository.save_position_recommendations(
                self._session_id, [rec["Theme"] for rec in recommendations]
            )
            for rec in recommendations:
                rec["Theme"] = theme_mapping.get(rec.get("Theme"))

        return ChatStreamReponse(self._session_id).create_tool_result_response(
            {
                "SearchKey": search_key,
                "TotolPositionCount": count,
                "Positions": position_summaries,
                "Recommendations": recommendations,
            }
        )

    def _save_chat(
        self,
        message: str,
        result: RunResultStreaming,
    ):
        """
        会話履歴のDB保存

        Args:
            message: ユーザー入力
            result: LLM回答
        """
        if self._should_save:
            if not self._session_created:
                # セッションはまだ作成されていないので、セッションを作成して、最初の挨拶と今回のユーザインプットをDBに保存する
                # 今回LLMレスポンスは_save_llm_messagesより保存します。
                self._create_session(result.input)
            else:
                # セッションは作成済みなので、ユーザーからのメッセージだけを保存する
                self._save_user_message(message)
            # LLMの返答を保存する
            # result.to_input_list()は普通のツールコールか、ハンドオフか区別できないので、result.new_itemsを使います。
            self._save_llm_messages(result.new_items)
        else:
            # 初めは挨拶なので、セッション作成はしないが、それ以降会話は続く場合保存するので、DB保存フラグをTrueにする
            self._should_save = True

    def _update_conversation_history(
        self,
        result: RunResultStreaming,
        stop_at_tool_calls: dict[str, ChatHistory],
    ):
        """
        会話履歴のメモリ保存

        Args:
            result: LLM回答
            stop_at_tool_calls: return_directツールコール入力と結果

        Returns:
            ポジション検索結果レスポンス
        """
        self._conversation_history[self._chat_key] = result.to_input_list()
        if stop_at_tool_calls:
            # stop_at_tool_callsにいまポジション検索ツールしかない。
            # ポジション詳細お問い合わせには、ポジション検索ツールが呼ばれないので、chat_keyは常にMAIN_CHAT_KEYのはず
            for _, history in stop_at_tool_calls.items():
                # 0.14 -> 0.16 updated, tool result of return_true tool is also contained in result.to_input_list()
                # TODO: If it is a long conversation, it isn't very efficient to remove result from history everytime.
                self._conversation_history[self._chat_key] = [
                    record
                    for record in self._conversation_history[self._chat_key]
                    if not (
                        record.get("type") == "function_call_output"
                        and record.get("call_id") == history.tool_call_id
                    )
                ]
                self._conversation_history[self._chat_key].append(
                    {
                        "type": "function_call_output",
                        "call_id": history.tool_call_id,
                        "output": POSITION_SEARCH_FAKE_RESULT,
                    }
                )

    def _create_session(self, conversation_history: list[TResponseInputItem]):
        """
        挨拶以上の会話した場合、初めてセッションデータをDB保存する

        Args:
            conversation_history: 会話履歴
        """
        secret_key = self._position_repository.get_or_create_session_key(
            self._session_id
        )
        self._chat_repository.create_chat_session(
            session_id=self._session_id,
            secret_key=secret_key.decode("utf-8"),
        )
        # 最初の挨拶メッセージ保存
        chat_histories: list[ChatHistory] = []
        for message in conversation_history:
            if message["type"] == "message":
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent.name,
                        # TODO: Not sure from when, id is always "__fake_id__"
                        message_id=message["id"] if "id" in message else None,
                        role=message["role"],
                        content=(
                            message["content"]
                            if isinstance(message["content"], str)
                            else message["content"][0]["text"]
                        ),
                    )
                )
            elif message["type"] == "function_call":
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent.name,
                        message_id=message["id"] if "id" in message else None,
                        role=LLMMessageRole.TOOL,
                        tool_call_id=message["call_id"],
                        tool_name=message["name"],
                        tool_input=json.loads(message["arguments"]),
                    )
                )
            elif message["type"] == "function_call_output":
                tool_call_id = message["call_id"]
                history = [
                    history
                    for history in chat_histories
                    if history.tool_call_id == tool_call_id
                ]
                if history:
                    history[0].content = message["output"]
                else:
                    self.logger.error(f"tool call id {tool_call_id} is NOT found.")
            elif message["type"] == "reasoning":
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent.name,
                        message_id=message["id"] if "id" in message else None,
                        role=LLMMessageRole.REASONING,
                        content=str.join(message["summary"]),
                    )
                )
            else:
                self.logger.error(f"Unsupported message type: {message}")

        self._chat_repository.add_chat_histories(chat_histories)
        self._session_created = True

    def _save_user_message(
        self,
        message: str,
    ):
        """
        ユーザーインプットをDB保存する

        Args:
            message: ユーザーインプット
        """
        self._chat_repository.add_chat_history(
            ChatHistory(
                session_id=self._session_id,
                position_id=self._position_id,
                active_agent=self._active_agent.name,
                role=LLMMessageRole.USER,
                content=message,
            )
        )

    def _save_llm_messages(
        self,
        items: list[RunItem],
    ):
        """
        ユーザーインプットをDB保存する

        Args:
            items: LLM回答
        """
        chat_histories: list[ChatHistory] = []

        for item in items:
            self._active_agent = item.agent
            if isinstance(item, MessageOutputItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent.name,
                        message_id=item.raw_item.id,
                        role=LLMMessageRole.ASSISTANT,
                        content=item.raw_item.content[0].text,
                    )
                )
            elif isinstance(item, HandoffCallItem) or isinstance(item, ToolCallItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent.name,
                        message_id=item.raw_item.id,
                        role=(
                            LLMMessageRole.TOOL
                            if isinstance(item, ToolCallItem)
                            else LLMMessageRole.HANDOFF
                        ),
                        tool_call_id=item.raw_item.call_id,
                        tool_name=item.raw_item.name,
                        tool_input=json.loads(item.raw_item.arguments),
                    )
                )
            elif isinstance(item, HandoffOutputItem) or isinstance(
                item, ToolCallOutputItem
            ):
                history = [
                    history
                    for history in chat_histories
                    if history.tool_call_id == item.raw_item["call_id"]
                ]
                if history:
                    history[0].content = item.raw_item["output"]
            elif isinstance(item, ReasoningItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        position_id=self._position_id,
                        active_agent=self._active_agent.name,
                        message_id=item.raw_item.id,
                        role=LLMMessageRole.REASONING,
                        content=str.join(item.raw_item.summary),
                    )
                )
            else:
                self.logger.error(f"Unsupported item type: {item}")

        self._chat_repository.add_chat_histories(chat_histories)

    def _get_position_detail(
        self,
        encrypted_position_id: str,
    ) -> Tuple[dict, dict, dict, str]:
        """
        キャッシュからポジション詳細、会社詳細、業界詳細を取得

        Args:
            encrypted_position_id: ポジションUUID

        Returns:
            成功時：ポジション詳細、会社詳細、業界詳細
            失敗時：エラーメッセージ
        """
        postion_detail = self._position_repository.get_position_detail(
            self._session_id,
            encrypted_position_id,
        )
        if not postion_detail:
            return (
                None,
                None,
                None,
                f"ポジション詳細が見つからなかった: {encrypted_position_id}",
            )

        company_detail = self._position_repository.get_company_detail(
            self._session_id,
            encrypted_position_id,
        )
        if not company_detail:
            return (
                None,
                None,
                None,
                f"会社詳細が見つからなかった: {encrypted_position_id}",
            )

        business_detail = self._position_repository.get_business_detail(
            self._session_id,
            encrypted_position_id,
        )
        if not business_detail:
            return (
                None,
                None,
                None,
                f"業界詳細が見つからなかった: {encrypted_position_id}",
            )

        return (postion_detail, company_detail, business_detail, None)

    def _get_agent(self, agent_name: str) -> Agent:
        """
        エージェントを取得する。

        Args:
            agent_name: エージェント名

        Returns:
            エージェント
        """
        agent = self._agents.get(agent_name)
        if not agent:
            raise Exception(f"Agent not found: {agent_name}")

        return agent

    # TODO: 複数のタブやWindowsで異なるポジション詳細が見れると思ってましたので、ポジション詳細Agentをクローンをしていますが、
    # 試したところ、確かポジション詳細が見れますが、websocketはもう利用できなくなります。
    # なので、複数タブやWindowsでのポジション詳細確認を辞めるか、websocketを利用できるようにするかを一度検討の必要があるかも
    # 前者（やめる）のほうがやりやすいかと思われます。
    # https://miidas-dev.slack.com/lists/TJWTV7T7C/F08BU50QS3Y?record_id=Rec093HHSAVAS
    def _get_or_create_position_agent(self, position_id: int) -> Tuple[Agent, bool]:
        """
        ポジションエージェントを取得する。

        Args:
            position_id: ポジションID

        Returns:
            Agent: ポジションエージェント
            bool: すでに存在しているか
        """
        exists = True
        position_agent = self._position_agents.get(position_id)
        if not position_agent:
            exists = False
            position_agent = self._get_agent(AgentName.POSITION_GUIDE).clone()
            self._position_agents[position_id] = position_agent

        return position_agent, exists
