import asyncio
import copy
import uuid
import aiohttp
from fastapi import status

from repositories.position_repo import PositionRepository
from services.base_service import BaseService


class PositionService(BaseService):
    """
    ポジション関連操作(APIリクエストやキャッシュ保存/取得)サービス
    """

    def __init__(
        self,
        position_repository: PositionRepository,
        api_url: str,
        session: aiohttp.ClientSession,
    ) -> None:
        super().__init__()

        self._position_repository = position_repository
        self._api_url = api_url
        self._session = session

    def _get_id_from_summary(
        self,
        session_id: str,
        encrypted_position_id: str,
        key: str,
    ) -> int | None:
        """
        変換後のポジションIDから本当のポジションIDを取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 変換後ポジションID
            key: メモリ保存キー

        Returns:
            バリュー
        """
        position_summary = self._position_repository.get_position_summary(
            session_id,
            encrypted_position_id,
        )
        return position_summary.get(key) if position_summary else None

    def get_position_id(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> int | None:
        """
        変換後のポジションIDから本当のポジションIDを取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 変換後ポジションID

        Returns:
            本当のポジションID
        """
        return self._get_id_from_summary(session_id, encrypted_position_id, "ID")

    def get_company_id(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> int | None:
        """
        変換後のポジションIDから本当の会社IDを取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 変換後ポジションID

        Returns:
            本当の会社ID
        """
        return self._get_id_from_summary(session_id, encrypted_position_id, "CompanyID")

    def get_business_id(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> int | None:
        """
        変換後のポジションIDから本当の業界IDを取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 変換後ポジションID

        Returns:
            本当の業界ID
        """
        return self._get_id_from_summary(
            session_id, encrypted_position_id, "BusinessID"
        )

    def remove_session(self, session_id):
        """
        キャッシュから指定セッションのデータを削除する

        Args:
            session_id: セッションID。
        """
        self._position_repository.remove_session(session_id)

    async def _api_request(
        self, method: str, url: str, session_id: str, **kwargs
    ) -> dict | list | None:
        headers = {
            "CHAT_SESSION_ID": session_id,
            # TODO: not generate a new request_id but use request_id from client
            "TOOL_REQUEST_ID": str(uuid.uuid4()),
        }
        kwargs.setdefault("headers", {}).update(headers)

        try:
            async with self._session.request(method, url, **kwargs) as response:
                if response.status == status.HTTP_200_OK:
                    return await response.json()

                self.logger.error(
                    f"API request to {url} failed with status {response.status}"
                )
                return None
        except asyncio.TimeoutError:
            self.logger.exception(f"Timeout calling API: {url}")
            return None
        except Exception as e:
            self.logger.exception(f"Error calling API {url}: {e}")
            return None

    async def get_position_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        ポジション詳細APIを呼び出して、ポジション詳細を取得してキャッシュに保存する

        Args:
            session_id: セッションID。
            encrypted_position_id: 変換後ポジションID

        Returns:
            ポジション詳細
        """
        position_id = self.get_position_id(session_id, encrypted_position_id)
        if not position_id:
            return None

        api_url = f"{self._api_url}/positions/{position_id}"
        self.logger.debug(f"ポジション詳細APIリクエスト: {api_url}")
        position_detail = await self._api_request("POST", api_url, session_id)

        if position_detail:
            self._position_repository.save_position_detail(
                session_id,
                encrypted_position_id,
                position_detail,
            )
        return position_detail

    async def get_company_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        会社詳細APIを呼び出して、会社詳細を取得してキャッシュに保存する

        Args:
            session_id: セッションID。
            encrypted_position_id: 変換後ポジションID

        Returns:
            会社詳細
        """
        company_id = self.get_company_id(session_id, encrypted_position_id)
        if not company_id:
            return None

        api_url = f"{self._api_url}/companies/{company_id}"
        self.logger.debug(f"会社詳細APIリクエスト: {api_url}")
        company_detail = await self._api_request("GET", api_url, session_id)

        if company_detail:
            self._position_repository.save_company_detail(
                session_id,
                encrypted_position_id,
                company_detail,
            )
        return company_detail

    async def get_business_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        業界詳細APIを呼び出して、業界詳細を取得してキャッシュに保存する

        Args:
            session_id: セッションID。
            encrypted_position_id: 変換後ポジションID

        Returns:
            業界詳細
        """
        business_id = self.get_business_id(session_id, encrypted_position_id)
        if not business_id:
            return None

        api_url = f"{self._api_url}/businesses/{business_id}"
        self.logger.debug(f"業界詳細APIリクエスト: {api_url}")
        business_detail = await self._api_request("GET", api_url, session_id)

        if business_detail:
            self._position_repository.save_business_detail(
                session_id,
                encrypted_position_id,
                business_detail,
            )
        return business_detail

    async def get_position_recommendation(
        self,
        session_id: str,
        encrypted_theme: str,
    ) -> list[dict] | None:
        """
        APIサーバーからおすすめポジションを取得する

        Args:
            session_id: セッションID。
            encrypted_theme: 暗号化されたおすすめテーマ
            search_conditions: ユーザーの検索条件

        Returns:
            おすすめのポジションリスト
        """
        theme = self._position_repository.get_position_recommendation_theme(
            session_id,
            encrypted_theme,
        )
        if not theme:
            return None

        search_conditions = self._position_repository.get_user_preferences(
            session_id,
        )

        api_url = f"{self._api_url}/positions/recommendations/{theme}"
        self.logger.debug(f"業界詳細APIリクエスト: {api_url}")
        response_data = await self._api_request(
            "POST",
            api_url,
            session_id,
            json=search_conditions,
        )

        if not response_data or "Positions" not in response_data:
            return None

        return self._position_repository.process_and_cache_positions(
            session_id,
            response_data["Positions"],
        )

    async def load_more(
        self, session_id: str, search_key: str, offset: int, limit: int
    ):
        position_ids = self._position_repository.get_search_result_position_ids(
            session_id,
            search_key,
            offset,
            limit,
        )

        api_url = f"{self._api_url}/positions/summaries"
        self.logger.debug(f"ポジションもっとみるAPIリクエスト: {api_url}")
        response_data = await self._api_request(
            "POST",
            api_url,
            session_id,
            json={"PositionIDs": position_ids},
        )

        if not response_data or "Positions" not in response_data:
            count = self._position_repository.remove_search_result_positions_ids(
                session_id,
                search_key,
                position_ids,
            )
            return (count, [])
        else:
            positions = response_data["Positions"]
            returned_ids = {p["ID"] for p in positions}
            non_avaible_position_ids = [
                pid for pid in position_ids if pid not in returned_ids
            ]
            positions = self._position_repository.process_and_cache_positions(
                session_id,
                positions,
            )

            return (
                (
                    self._position_repository.remove_search_result_positions_ids(
                        session_id,
                        search_key,
                        non_avaible_position_ids,
                    )
                    if non_avaible_position_ids
                    else self._position_repository.get_search_result_count(
                        session_id,
                        search_key,
                    )
                ),
                positions,
            )
