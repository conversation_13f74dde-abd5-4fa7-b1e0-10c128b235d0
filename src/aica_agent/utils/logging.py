import logging
from typing import Optional
from contextvars import ContextVar

# TODO: ContextVarの本当の範囲はまだはっきりわかっていない
session_id_var: ContextVar[Optional[str]] = ContextVar("Session ID", default=None)

old_factory = logging.getLogRecordFactory()


def record_factory(*args, **kwargs):
    record = old_factory(*args, **kwargs)
    record.session_id = session_id_var.get()
    return record


def set_session_id(session_id: str) -> None:
    session_id_var.set(session_id)


def clear_session_id() -> None:
    session_id_var.set(None)
