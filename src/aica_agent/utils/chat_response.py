from enum import StrEnum
import json
from typing import Self
import uuid
from pydantic import BaseModel


class MessageType(StrEnum):
    MESSAGE = "message"
    POSITION_SEARCH_RESULT = "position_search_result"
    ERROR = "error"
    END = "end"


class ChatStreamReponseModel(BaseModel):
    session_id: str
    message_id: str
    message_type: MessageType
    position_id: str | None = None
    message: str

class ChatStreamReponse:
    def __init__(
        self,
        session_id: str,
        position_id: str = None,
        message_id: str = None,
    ):
        self._model = ChatStreamReponseModel(
            session_id=session_id,
            position_id=position_id,
            message_id=message_id if message_id else str(uuid.uuid4()),
            message_type=MessageType.MESSAGE,
            message="",
        )

    def create_end_response(self) -> ChatStreamReponseModel:
        """
        １回のインプットに対してのレスポン終了

        Returns:
            LLM回答終了レスポンス
        """
        return self.create_response(MessageType.END, "")

    def create_tool_result_response(
        self,
        result: dict,
    ) -> ChatStreamReponseModel:
        """
        return_direct=Trueのツールの場合、実行結果をそのままレスポンスする

        Args:
            result: ツール実行結果

        Returns:
            ツール実行結果
        """
        return self.create_response(
            MessageType.POSITION_SEARCH_RESULT,
            json.dumps(result),
        )

    def create_message_response(
        self,
        message: str,
    ) -> ChatStreamReponseModel:
        """
        LLM回答をレスポンスする

        Args:
            message: LLM回答

        Returns:
            LLM回答
        """
        return self.create_response(
            MessageType.MESSAGE,
            message,
        )

    def create_error_response(
        self,
        error: str,
    ) -> ChatStreamReponseModel:
        """
        エラー発生した場合のレスポンス

        Args:
            error: エラーメッセージ

        Returns:
            エラーメッセージ
        """
        return self.create_response(
            MessageType.ERROR,
            error,
        )

    def create_response(
        self,
        message_type: MessageType,
        message: str,
    ) -> ChatStreamReponseModel:
        self._model.message_type = message_type
        self._model.message = message
        return self._model
