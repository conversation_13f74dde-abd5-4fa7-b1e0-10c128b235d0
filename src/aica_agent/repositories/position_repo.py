import copy
import logging
from typing import Any

from const import LOGGER_PREFIX
from utils.crypt import create_secrete_key, decrypt, encrypt
from utils import sync_dict


# 本当のポジションIDを外部公開しないため、ポジションツール検索結果をメモリに保存し、ユーザーがポジションの「詳細を見る」を押すときに、ここから本当のポジションIDを取得し、APIサーバからポジション詳細を取得する。
# TODO: セッション再開の場合、もし以前の履歴はまだ見れるなら、表示するたびに、このマッピングを作成する必要があります。
# TODO: エラーがあって接続が切れて再属した場合、セッションはクリアされたので、ポジションカードは画面に表示されていても、実はクリックできない。
class PositionRepository:
    """
    セッション毎にポジション関連情報のキャッシュ
    """

    def __init__(self):
        """
        下記のポジション情報キャッシュ初期化
        ・暗号化・復号化のキー（セッション毎異なる）
        ・ポジション検索結果
        ・ポジション詳細表示に関連する情報
            ・ポジション詳細
            ・会社詳細
            ・業界詳細
        ・ユーザー検索条件
        """
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )
        self._session_keys = sync_dict.SynchronizedDict[str, bytes]()
        self._position_search_result_ids = sync_dict.SynchronizedDict[
            str, dict[str, list[int]]
        ]()
        self._position_summaries = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._position_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._company_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._business_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._user_preferences = sync_dict.SynchronizedDict[str, dict[str, Any]]()

    def get_or_create_session_key(self, session_id: str) -> bytes:
        """
        暗号化・復号化キー取得／生成

        Args:
            session_id: セッションID。

        Returns:
            暗号化・復号化キー
        """
        if session_id not in self._session_keys:
            self._session_keys[session_id] = create_secrete_key()
        return self._session_keys[session_id]

    def save_session_key(self, session_id: str, secret_key: str):
        self._session_keys[session_id] = secret_key.encode()

    def _encrypt_id(self, session_id: str, real_id: str) -> str:
        """
        ポジションIDを暗号化する

        Args:
            session_id: セッションID。
            real_id: 実際のポジションID

        Returns:
            暗号化されたポジションID
        """
        key = self.get_or_create_session_key(session_id)
        return encrypt(key, real_id)

    def _decrypt_id(self, session_id: str, encrypted_id: str) -> str:
        """
        ポジションIDを復号化する

        Args:
            session_id: セッションID。
            encrypted_id: 暗号化されたポジションID

        Returns:
            復号化されたポジションID
        """
        key = self.get_or_create_session_key(session_id)
        return decrypt(key, encrypted_id)

    def save_search_result_position_ids(
        self, session_id: str, search_key: str, position_ids: list[int]
    ) -> int:
        """
        ポジション検索結果IDをキャッシュする

        Args:
            session_id: セッションID。
            search_key: 検索キー
            position_ids: 検索結果のポジションIDリスト

        Returns:
            ポジション検索結果数
        """
        if not position_ids:
            return

        seen = set()
        unique_position_ids = []
        for pid in position_ids:
            if pid not in seen:
                seen.add(pid)
                unique_position_ids.append(pid)

        session_data = self._position_search_result_ids.setdefault(session_id, {})
        session_data[search_key] = unique_position_ids

        return len(unique_position_ids)

    def get_search_result_position_ids(
        self,
        session_id: str,
        search_key: str,
        offset: int,
        limit: int,
    ) -> list[int]:
        """
        ポジション検索結果IDを取得

        Args:
            session_id: セッションID。
            search_key: 検索キー

        Returns:
            ポジション検索結果IDリスト
        """
        session_data = self._position_search_result_ids.setdefault(session_id, {})
        return session_data.get(search_key, [])[offset : offset + limit]

    def get_search_result_count(self, session_id: str, search_key: str) -> int:
        """
        ポジション検索結果数を取得

        Args:
            session_id: セッションID。
            search_key: 検索キー

        Returns:
            ポジション検索結果数
        """
        session_data = self._position_search_result_ids.setdefault(session_id, {})
        return len(session_data.get(search_key, []))

    def remove_search_result_positions_ids(
        self,
        session_id: str,
        search_key: str,
        position_ids: list[int],
    ) -> int:
        """
        ポジション検索結果IDを削除

        Args:
            session_id: セッションID。
            search_key: 検索キー
            position_ids: 削除するポジションIDリスト
        """
        session_data = self._position_search_result_ids.setdefault(session_id, {})
        if search_key in session_data:
            session_data[search_key] = [
                pid for pid in session_data[search_key] if pid not in position_ids
            ]

        return len(session_data.get(search_key, []))

    def process_and_cache_positions(
        self,
        session_id: str,
        positions: list[dict[str, Any]],
    ) -> list[dict[str, Any]]:
        """
        ポジション検索結果をキャッシュする

        Args:
            session_id: セッションID。
            positions: 検索結果

        Returns:
            処理後のポジションリスト
        """
        if not positions:
            return []

        session_data = self._position_summaries.setdefault(session_id, {})
        session_data.update(
            # 下に`positions`を変更するので、元の値を保持するために`deepcopy`を利用しています。
            {str(position["ID"]): position for position in copy.deepcopy(positions)}
        )

        for position in positions:
            real_id = str(position["ID"])
            try:
                position["ID"] = self._encrypt_id(session_id, real_id)
            except Exception:
                self._logger.exception("Failed to encrypt position ID: %s", real_id)
                return []
            # CompanyIDとBusinessIDは内部IDなので、外部公開しない
            position.pop("CompanyID", None)
            position.pop("BusinessID", None)
        return positions

    def get_position_summary_count(
        self,
        session_id,
    ) -> int:
        return len(self._position_summaries.setdefault(session_id, {}))

    def get_position_summary(
        self,
        session_id,
        encrypted_position_id,
    ) -> dict[str, dict] | None:
        """
        キャッシュからポジション検索結果を取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 暗号化されたポジションID

        Returns:
            ポジション検索結果
        """
        try:
            real_id = self._decrypt_id(session_id, encrypted_position_id)
            saved_positions = self._position_summaries.setdefault(session_id, {})
            return (
                copy.deepcopy(saved_positions.get(real_id))
                if real_id in saved_positions
                else None
            )
        except Exception:
            return None

    def save_position_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
        position_detail: dict,
    ):
        """
        ポジション詳細APIレスポンスをキャッシュする

        Args:
            session_id: セッションID。
            encrypted_position_id: 暗号化されたポジションID
            position_detail: ポジション詳細APIレスポンス
        """
        try:
            real_id = self._decrypt_id(session_id, encrypted_position_id)
            position_details = self._position_details.setdefault(session_id, {})
            position_details[real_id] = position_detail
        except Exception:
            self._logger.exception("ポジションID復号化: %s", encrypted_position_id)

    def get_position_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        キャッシュからポジション詳細APIレスポンスを取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 暗号化されたポジションID

        Returns:
            ジション詳細APIレスポンス
        """
        try:
            real_id = self._decrypt_id(session_id, encrypted_position_id)
            position_details = self._position_details.setdefault(session_id, {})
            return (
                copy.deepcopy(position_details.get(real_id))
                if real_id in position_details
                else None
            )
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )
            return None

    def save_company_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
        company_detail: dict,
    ):
        """
        会社詳細APIレスポンスをキャッシュする

        Args:
            session_id: セッションID。
            encrypted_position_id: 暗号化されたポジションID
            company_detail: 会社詳細APIレスポンス
        """
        try:
            real_id = self._decrypt_id(session_id, encrypted_position_id)
            company_details = self._company_details.setdefault(session_id, {})
            company_details[real_id] = company_detail
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )

    def get_company_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        キャッシュから会社詳細APIレスポンスを取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 暗号化されたポジションID

        Returns:
            会社詳細APIレスポンス
        """
        try:
            real_id = self._decrypt_id(session_id, encrypted_position_id)
            company_details = self._company_details.setdefault(session_id, {})
            return (
                copy.deepcopy(company_details.get(real_id))
                if real_id in company_details
                else None
            )
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )
            return None

    def save_business_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
        business_detail: dict,
    ):
        """
        業界詳細APIレスポンスをキャッシュする

        Args:
            session_id: セッションID。
            encrypted_position_id: 暗号化されたポジションID
            business_detail: 業界詳細APIレスポンス
        """
        try:
            real_id = self._decrypt_id(session_id, encrypted_position_id)
            business_details = self._business_details.setdefault(session_id, {})
            business_details[real_id] = business_detail
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )

    def get_business_detail(
        self,
        session_id: str,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        キャッシュから業界詳細APIレスポンスを取得

        Args:
            session_id: セッションID。
            encrypted_position_id: 暗号化されたポジションID

        Returns:
            業界詳細APIレスポンス
        """
        try:
            real_id = self._decrypt_id(session_id, encrypted_position_id)
            business_details = self._business_details.setdefault(session_id, {})
            return (
                copy.deepcopy(business_details.get(real_id))
                if real_id in business_details
                else None
            )
        except Exception:
            self._logger.exception("ポジションID: %s", encrypted_position_id)
            return None

    def save_position_recommendations(
        self,
        session_id: str,
        recommendation_themes: list[str],
    ) -> dict[str, str]:
        """
        おすすめのリンクマッピング作成

        Args:
            session_id: セッションID。
            recommendation_themes: おすすめパスリスト

        Returns:
            レコメンドリンクマッピング
        """
        return {
            theme: self._encrypt_id(session_id, theme)
            for theme in recommendation_themes
        }

    def get_position_recommendation_theme(
        self,
        session_id: str,
        encrypted_theme: str,
    ) -> str | None:
        """
        おすすめのリンクマッピングからパスを取得

        Args:
            session_id: セッションID。
            encrypted_theme: 暗号化されたおすすめテーマ

        Returns:
            おすすめテーマ
        """
        try:
            return self._decrypt_id(session_id, encrypted_theme)
        except Exception:
            return None

    def save_user_preference(
        self,
        session_id: str,
        preference: dict[str, Any],
    ):
        """
        ユーザーの検索条件をキャッシュする

        Args:
            session_id: セッションID。
            preference: ユーザーの検索条件
        """
        self._logger.debug(f"save_user_preference: {preference}")
        session_data = self._user_preferences.setdefault(session_id, {})
        session_data.update(preference)

    def get_user_preferences(
        self,
        session_id: str,
    ) -> dict | None:
        """
        キャッシュからユーザーの検索条件を取得

        Args:
            session_id: セッションID。

        Returns:
            ユーザーの検索条件
        """
        session_data = self._user_preferences.setdefault(session_id, {})
        return copy.deepcopy(session_data) if session_data else {}
