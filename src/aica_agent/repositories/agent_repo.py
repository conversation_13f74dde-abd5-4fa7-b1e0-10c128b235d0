from contextlib import AbstractContextManager
from typing import Callable
from sqlalchemy import select
from sqlalchemy.orm import Session, joinedload

from domain.enitites.agent import Agent
from domain.enitites.agent_tool import AgentTool
from domain.enitites.workflow import Workflow


class AgentRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory

    def get_agents(self) -> list[Agent]:
        """
        ・エージェント
        ・エージェントに紐づいたツール
        ・ハンドオフ先のエージェント
        を取得する。

        Returns:
            エージェント一覧
        """
        with self._session_factory() as session:
            stmt = select(Agent).options(
                joinedload(Agent.tools).joinedload(AgentTool.tool),
                joinedload(Agent.next_agents).joinedload(Workflow.dest_agent),
            )
            return session.scalars(stmt).unique().all()
