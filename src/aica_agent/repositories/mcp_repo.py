from contextlib import AbstractContextManager
from typing import Callable
from sqlalchemy.orm import Session

from domain.enitites.mcp_prompt import Mcp<PERSON>rompt
from domain.enitites.mcp_tool_definition import McpToolDefinition


# TODO: いまはプロンプトはもうMCP管理ではないので、agent_repoに移行したほうが良いかも
# TODO: プロンプトのDBマイグレーションファイルもこちらのプロジェクトに移行したほうが方が良いかも
class McpRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory

    def get_prompt_by_agentname(self, agent_name: str) -> str | None:
        """
        エージェント名に対応するプロンプトを取得する。

        Args:
            agent_name: エージェント名

        Returns:
            プロンプト or None
        """
        with self._session_factory() as session:
            prompt = (
                session.query(McpPrompt)
                .filter(McpPrompt.name == agent_name, McpPrompt.deleted_at.is_(None))
                .first()
            )
            if prompt:
                return prompt.prompt
            else:
                return None
