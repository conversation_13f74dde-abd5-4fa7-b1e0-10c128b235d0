from contextlib import Abstract<PERSON>ontextManager
from typing import Callable, <PERSON><PERSON>, Optional
from sqlalchemy import select, func
from sqlalchemy.orm import Session, joinedload, aliased

from domain.enitites.chat_history import ChatHistory
from domain.enitites.chat_session import ChatSession
from utils.crypt import create_secrete_key


class ChatRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory

    # TODO: batchでやる
    # def clean_data(self):
    #     """
    #     会話履歴クリーニング
    #     - 30日前ののデータを物理削除
    #     - 2日前のデータを論理削除
    #     """
    #     SessionAlias = aliased(ChatSession)
    #     now = datetime.now()
    #     two_weeks_ago = now - timedelta(days=2)
    #     one_month_ago = now - timedelta(days=30)

    #     with self._session_factory() as session:
    #         # 1. 30日前ののデータを物理削除
    #         session.execute(
    #             delete(SessionAlias).where(
    #                 SessionAlias.created_at < one_month_ago,
    #             )
    #         )

    #         # 2. 2週間前のデータを論理削除
    #         session.execute(
    #             update(SessionAlias)
    #             .where(
    #                 SessionAlias.created_at < two_weeks_ago,
    #                 SessionAlias.deleted_at.is_(None),
    #             )
    #             .values(deleted_at=now)
    #         )
    #         session.commit()

    def init_chat_session(
        self,
        chat_session_id: str,
    ) -> Tuple[Optional[ChatSession], bool]:
        """
        chat_session_idに対応する会話履歴も取得する。

        Args:
            chat_session_id: セッションID

        Returns:
            会話履歴 or None
        """
        with self._session_factory() as session:
            # self.clean_data()

            HistoryAlias = aliased(ChatHistory)
            stmt = (
                select(ChatSession)
                .options(
                    joinedload(
                        ChatSession.histories.of_type(HistoryAlias).and_(
                            HistoryAlias.deleted_at.is_(None)
                        )
                    ),
                    joinedload(ChatSession.user_profile),
                )
                .filter(
                    ChatSession.session_id == chat_session_id,
                    ChatSession.deleted_at.is_(None),
                )
                .order_by(HistoryAlias.id)
            )
            chat_session = session.scalars(stmt).unique().first()
            if chat_session:
                if not chat_session.secret_key:
                    secret_key = create_secrete_key()
                    chat_session.secret_key = secret_key.decode()
                    session.add(chat_session)
                    session.commit()
                return (chat_session, True)
            else:
                count_stmt = (
                    select(func.count())
                    .select_from(ChatSession)
                    .where(
                        ChatSession.session_id == chat_session_id,
                        ChatSession.deleted_at.isnot(None),
                    )
                )
                count = session.execute(count_stmt).scalar_one()
                return (None, count > 0)

    def create_chat_session(
        self,
        session_id: str,
        secret_key: str,
    ):
        """
        会話セッション作成する。

        Args:
            chat_session_id: セッションID
        """
        with self._session_factory() as session:
            chat_session = ChatSession(
                session_id=session_id,
                secret_key=secret_key,
            )
            session.add(chat_session)
            session.commit()

    def add_chat_histories(
        self,
        chat_histories: list[ChatHistory],
    ):
        """
        複数会話を追加する。

        Args:
            chat_histories: 会話履歴
        """
        with self._session_factory() as session:
            for chat_history in chat_histories:
                session.add(chat_history)
                session.commit()

    def add_chat_history(
        self,
        chat_history: ChatHistory,
    ) -> None:
        """
        １つの会話を追加する。

        Args:
            chat_history: 会話履歴
        """
        with self._session_factory() as session:
            session.add(chat_history)
            session.commit()
