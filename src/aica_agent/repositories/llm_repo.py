from enum import StrEnum
from typing import Tuple
from agents import Agent, ModelSettings
from dependency_injector import resources
import logging

from agents.extensions.models.litellm_model import LitellmModel

from agents.mcp import MCPServerSse
from agents.mcp.util import MCPUtil

from const import LOGGER_PREFIX
from repositories.agent_repo import AgentRepository
from repositories.mcp_repo import McpRepository


class LLMModel(StrEnum):
    """
    LLMモデル名
    [src/aica_agent/config.yml]の[model_list]の[model_name]と１対１
    """

    BEDROCK_CLAUDE_V1 = "bedrock-claude-v1"
    OPENAI_GPT_4_1 = "openai-gpt-4.1"


class NotSupportedProvider(Exception):
    """
    LLMModelにないモデル
    """

    def __init__(self, message):
        super().__init__(message)


class AgentName(StrEnum):
    """
    エージェント名
    テーブル[agents]のカラム[name]となります。
    動的にアクティブエージェントを切り替えるときに利用するためソースにも定義しています。
    """

    DEFAULT_AGENT = "DefaultAgent"
    CAREER_ADVISOR = "CareerAdvisor"
    POSITION_GUIDE = "PositionGuide"


class LLMRepository(resources.AsyncResource):
    def __init__(self) -> None:
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )

        self._mcp_server = None
        self._agents: dict[str, dict[str, tuple[Agent, bool]]] = {}

    async def init(
        self,
        mcp_url,
        model_list,
        agent_repository: AgentRepository,
        mcp_repository: McpRepository,
    ):
        """
        LLMRepository初期化
        ・MCPサーバ接続
        ・ワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            mcp_url: MCPサーバーURL
            model_list: LLMモデル一覧
            agent_repository: エージェントリポジトリ
            mcp_repository: MCPリポジトリ
        """
        self._mcp_server = MCPServerSse(
            name="AICA Server",
            params={
                "url": mcp_url,
            },
            client_session_timeout_seconds=60,
        )
        try:
            await self._mcp_server.__aenter__()
            await self._init_agents(model_list, agent_repository, mcp_repository)
        except Exception as e:
            self._logger.exception(f"Agent初期化失敗（MCPサーバー： {mcp_url}）: {e}")
            await self.shutdown()
            raise

        return self

    async def shutdown(self, _: None):
        """
        LLMRepository終了処理
        ・MCPサーバ切断
        """
        if self._mcp_server is not None:
            try:
                await self._mcp_server.__aexit__(None, None, None)
            except Exception as e:
                self._logger.warning(
                    f"MCPサーバへの接続切断失敗: {e}",
                    exc_info=True,
                )
        else:
            self._logger.info("MCPサーバがないので、接続切断は不要")

    # TODO: MCPサーバが再起動されたら、すでに接続できたコネクションは切れてしまうので、Agentサーバも再起動しMCPサーバに再接続しない限り、使えなくなります。
    # そのため、使えなくなったときに再接続する仕組みが必要
    # async def reconnect(self):
    #     """
    #     MCPサーバ再起動などのため、接続切れば場合、エージェント初期化時のMCPサーバ接続はもう使えなくなりますので、
    #     ツール呼び出しができなくなります。
    #     そのため、再接続の手段が必要ですが、まだやってない
    #     """
    #     if self._mcp_server is not None:
    #         try:
    #             await self._mcp_server.__aexit__(None, None, None)
    #         except Exception as e:
    #             self._logger.warning(f"Error during MCP server disconnect: {e}")

    #     await self._mcp_server.__aenter__()
    #     await self._init_agents(self._model_list, self._agent_repository)

    async def _init_agents(
        self,
        model_list,
        agent_repository: AgentRepository,
        mcp_repository: McpRepository,
    ):
        """
        src/aica_agent/config.ymlの[model_list]をもとにワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            model_list: LLMモデル一覧。src/aica_agent/config.ymlの[model_list]
            agent_repository: エージェントリポジトリ（エージェント設定；デフォルトエージェントや紐づいたツール名など）
            mcp_repository: MCPリポジト（エージェントのシステムプロンプト、ツール定義）
        """
        tools = await MCPUtil.get_all_function_tools([self._mcp_server], True)
        tool_names = [tool.name for tool in tools]
        self._logger.debug(f"MCP Tools: {tool_names}")
        agents = agent_repository.get_agents()
        for model in model_list:
            if model["model_name"] not in [e.value for e in LLMModel]:
                self._logger.error(f"Unsupported model: {model.model_name}")
                continue
            model_name = model["model_settings"]["model"]
            model_settings = ModelSettings(
                temperature=model["model_settings"]["temperature"],
                top_p=model["model_settings"]["top_p"],
            )
            react_agents = {}
            for agent in agents:
                prompt = mcp_repository.get_prompt_by_agentname(agent.name)
                if prompt is None:
                    # DB定義ミスった場合、起動エラー
                    self._logger.error(
                        f"Agent {agent.name} のシステムプロンプトがDBに存在しない。"
                    )
                    raise Exception(
                        f"Agent {agent.name} のシステムプロンプトがDBに存在しない。"
                    )
                else:
                    agent_prompt = prompt
                    self._logger.debug(
                        f"Agent {agent.name}のシステムプロンプト: {agent_prompt}"
                    )

                agent_tool_names = [tool.tool_name for tool in agent.tools]
                non_existent_tools = [
                    tool_name
                    for tool_name in agent_tool_names
                    if tool_name not in tool_names
                ]
                if non_existent_tools:
                    # DB定義ミスった場合、起動エラー
                    self._logger.error(
                        f"Agent {agent.name}のツール{non_existent_tools}がDBに存在しない。"
                    )
                    raise Exception(
                        f"Agent {agent.name}のツール{non_existent_tools}がDBに存在しない。"
                    )
                agent_tools = [tool for tool in tools if tool.name in agent_tool_names]
                react_agent = Agent(
                    model=LitellmModel(model_name),
                    model_settings=model_settings,
                    name=agent.name,
                    instructions=agent_prompt,
                    tools=agent_tools,
                )
                stop_at_tool_names = [
                    tool.tool_name
                    for tool in agent.tools
                    if tool.tool and tool.tool.return_direct
                ]
                if stop_at_tool_names:
                    react_agent.tool_use_behavior = {
                        "stop_at_tool_names": stop_at_tool_names,
                    }

                react_agents[agent.name] = (
                    react_agent,
                    agent.next_agents,
                    agent.default_agent,
                )

            for _, (agent, next_agents, _) in react_agents.items():
                if next_agents:
                    agent.handoffs = [
                        react_agents[next_agent.dest_agent.name][0]
                        for next_agent in next_agents
                    ]

            self._agents[model["model_name"]] = {
                agent_name: (agent, default_agent)
                for agent_name, (agent, _, default_agent) in react_agents.items()
            }

    def clone_agents(self, provider: str) -> dict[str, Tuple[Agent, bool]]:
        """
        エージェント群をクローンする。

        Args:
            provider: LLMプロバイダー

        Returns:
            エージェント群
        """
        model_name = ""
        if provider == LLMModel.BEDROCK_CLAUDE_V1:
            model_name = LLMModel.BEDROCK_CLAUDE_V1.value
        elif provider == LLMModel.OPENAI_GPT_4_1:
            model_name = LLMModel.OPENAI_GPT_4_1.value
        else:
            self._logger.exception(f"Unsupported provider: {provider}")
            raise NotSupportedProvider(f"Unsupported provider: {provider}")

        # https://miidas-dev.slack.com/archives/C08CPHXCZ08/p1750994467102159
        # 性能面（あくまで推測、負荷テストで確認する必要があります）やThread-safeから考えると、
        # グローバル１つのAgentではなく、セッションごとに各自のAgentを持つ
        cloned_agents = {
            k: (v[0].clone(), v[1]) for k, v in self._agents[model_name].items()
        }
        return cloned_agents
