from contextlib import AbstractContextManager
from sqlalchemy.orm import Session
from typing import Any, Callable

from domain.enitites.user_profile import UserProfile

class UserRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory

    def update_job_search_filter(
        self,
        session_id: str,
        preference: dict[str, Any],
    ):
        with self._session_factory() as session:
            user_profile = session.query(UserProfile).filter_by(session_id=session_id).first()
            if user_profile:
                user_profile.job_search_filter = preference
            else:
                user_profile = UserProfile(session_id=session_id, job_search_filter=preference)
                session.add(user_profile)
            session.commit()
