from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ger, String
from sqlalchemy.orm import Mapped, mapped_column

from database import Base


# TODO: return_redirectはagent_toolsに移行する
class McpToolDefinition(Base):
    __tablename__ = "mcp_tool_definitions"

    id = Column(Integer, primary_key=True)
    name: Mapped[String] = mapped_column(ForeignKey("agent_tools.tool_name"))
    return_direct = Column(Boolean)
