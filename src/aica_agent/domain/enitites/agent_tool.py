from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .mcp_tool_definition import McpToolDefinition
from database import Base


class AgentTool(Base):
    __tablename__ = "agent_tools"

    id = Column(Integer, primary_key=True)
    agent_id: Mapped[Integer] = mapped_column(ForeignKey("agents.id"))
    tool_name = Column(String)
    tool: Mapped[McpToolDefinition] = relationship(
        primaryjoin="McpToolDefinition.name == AgentTool.tool_name"
    )
