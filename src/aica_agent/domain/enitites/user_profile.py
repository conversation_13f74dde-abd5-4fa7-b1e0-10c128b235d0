from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON>ey, Integer, String, JSON, ARRAY
from sqlalchemy.orm import Mapped, mapped_column

from database import Base


class UserProfile(Base):
    __tablename__ = "user_profiles"

    id = Column(Integer, primary_key=True)
    session_id: Mapped[String] = mapped_column(ForeignKey("chat_sessions.session_id"))
    user_name = Column(String, nullable=True)
    user_purpose = Column(String, nullable=True)
    interest_tendency = Column(String, nullable=True)
    job_search_motivation = Column(String, nullable=True)
    current_job_experience_years = Column(Integer, nullable=True)
    current_job_description = Column(String, nullable=True)
    job_search_filter = Column(JSON, nullable=True)
    job_feedback_positive = Column(ARRAY(String), nullable=True)
    job_feedback_negative = Column(ARRAY(String), nullable=True)
