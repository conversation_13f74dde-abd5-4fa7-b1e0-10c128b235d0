from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, relationship

from database import Base

from .agent_tool import AgentTool
from .workflow import Workflow


class Agent(Base):
    __tablename__ = "agents"

    id = Column(Integer, primary_key=True)
    name = Column(String)
    description = Column(String)
    default_agent = Column(Boolean)

    tools: Mapped[list[AgentTool]] = relationship(
        primaryjoin="Agent.id == AgentTool.agent_id"
    )
    next_agents: Mapped[list[Workflow]] = relationship(
        primaryjoin="Agent.id == Workflow.src_agent_id"
    )
