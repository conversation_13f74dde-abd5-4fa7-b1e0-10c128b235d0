from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.orm import Mapped, relationship

from database import Base

from .chat_history import ChatHistory
from .user_profile import UserProfile


class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True)
    session_id = Column(String, unique=True)
    secret_key = Column(String)
    summary = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.now())
    deleted_at = Column(DateTime, nullable=True)

    histories: Mapped[list[ChatHistory]] = relationship(
        primaryjoin="ChatSession.session_id == ChatHistory.session_id"
    )
    user_profile: Mapped[UserProfile] = relationship(
        primaryjoin="ChatSession.session_id == UserProfile.session_id"
    )
