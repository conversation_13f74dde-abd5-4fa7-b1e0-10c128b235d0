import json
import logging
from typing import Optional
import uuid
from fastapi import (
    APIRouter,
    Depends,
    WebSocket,
    WebSocketDisconnect,
    status,
)
from fastapi.responses import JSONResponse
from dependency_injector.wiring import Provide, inject

from agents import trace

from containers import Container
from const import LOGGER_PREFIX
from repositories.llm_repo import LLMModel
from services.chat_service import ChatService, ChatStreamReponse
from services.position_service import PositionService
from utils.logging import set_session_id, clear_session_id

import faulthandler

faulthandler.enable()

import asyncio

asyncio.get_event_loop().set_debug(True)

logger = logging.getLogger(f"{LOGGER_PREFIX}.{__name__}")
router = APIRouter(prefix="/agent")


@router.get("/health")
def health():
    return {"status": "OK"}


@router.get("/positions/search/{session_id}/{search_key}/{offset}")
@inject
async def load_more_positions(
    session_id: str,
    search_key: str,
    offset: int,
    limit: int = 5,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    count, positions = await position_service.load_more(
        session_id,
        search_key,
        offset,
        limit,
    )
    return {
        "TotolPositionCount": count,
        "Positions": positions,
    }


@router.get("/positions/{session_id}/{encrypted_position_id}")
@inject
async def position_detail(
    session_id: str,
    encrypted_position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    detail = await position_service.get_position_detail(
        session_id, encrypted_position_id
    )
    if detail:
        return detail
    else:
        # HTTPException利用の場合、同時にたくさんリクエストが来ている場合、サーバーが死んでしまうので、代わりにJSONResponseを利用します
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"error": "求人詳細が見つからなかった。"},
        )


@router.get("/companies/{session_id}/{encrypted_position_id}")
@inject
async def company_detail(
    session_id: str,
    encrypted_position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    detail = await position_service.get_company_detail(
        session_id, encrypted_position_id
    )
    if detail:
        return detail
    else:
        # Use JSONResponse instead of HTTPException to avoid concurrency issues
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"error": "会社詳細が見つからなかった。"},
        )


@router.get("/businesses/{session_id}/{encrypted_position_id}")
@inject
async def business_detail(
    session_id: str,
    encrypted_position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    detail = await position_service.get_business_detail(
        session_id, encrypted_position_id
    )
    if detail:
        return detail
    else:
        # Use JSONResponse instead of HTTPException to avoid concurrency issues
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"error": "業界詳細が見つからなかった。"},
        )


@router.get("/positions/recommendations/{session_id}/{search_key}/{encrypted_theme}")
@inject
async def position_recommendations(
    session_id: str,
    search_key: str,
    encrypted_theme: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    positions = await position_service.get_position_recommendation(
        session_id,
        encrypted_theme,
    )

    if not positions:
        # raise HTTPException(status_code=404...) will make the server hang.
        # So instead of raising HTTPException, return a normal response
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "SearchKey": search_key,
                "Positions": [],
            },
        )

    return {
        "SearchKey": search_key,
        "Positions": positions,
    }


@router.websocket("/chat")
@inject
async def chat(
    websocket: WebSocket,
    session_id: Optional[str] = None,
    chat_service: ChatService = Depends(Provide[Container.chat_service]),
    # position_service: Any = Depends(Provide[Container.position_service]),
):

    await websocket.accept()

    try:
        if session_id is None:
            session_id = str(uuid.uuid4())
        set_session_id(session_id)
        logger.debug("Session ID: %s", session_id)

        await handle_chat_session(websocket, session_id, chat_service)
    except WebSocketDisconnect as e:
        logger.debug("Websocket切断: %s", e, exc_info=True)
    except Exception as e:
        logger.exception("予期しないエラー: %s", e)
    finally:
        clear_session_id()
        # TODO: don't clear session immediately when websocket is disconnected.
        # position_service.remove_session(session_id)


async def handle_chat_session(
    websocket: WebSocket,
    session_id: str,
    chat_service: ChatService,
):
    session_id = await chat_service.init_session(
        session_id, LLMModel.OPENAI_GPT_4_1.value
    )
    # session_id = await chat_service.init_session(session_id, LLMModel.BEDROCK_CLAUDE_V1.value)
    if session_id:
        await process_chat_messages(websocket, session_id, chat_service)
    else:
        await send_error_response(websocket, session_id)


async def send_error_response(
    websocket: WebSocket,
    session_id: str,
):
    error_response = ChatStreamReponse(
        session_id,
    ).create_error_response("Failed to initialize session")
    await websocket.send_text(error_response.model_dump_json())


async def process_chat_messages(
    websocket: WebSocket,
    session_id: str,
    chat_service: ChatService,
):
    with trace("AICA workflow", trace_id=f"trace_{session_id}"):
        while True:
            try:
                message = await websocket.receive_text()
                input = json.loads(message)
                input.setdefault("prev_page", "")
                input.setdefault("current_page", "")
                input.setdefault("position_id", "")
                input.setdefault("message", "")
                if input["message"]:
                    async for chunk in chat_service.chat(input):
                        await websocket.send_text(chunk.model_dump_json())
            except json.JSONDecodeError as e:
                logger.exception("Invalid JSON: %s", e, exc_info=True)
