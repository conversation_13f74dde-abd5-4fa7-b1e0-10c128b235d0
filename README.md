# 概要

AICAプロジェクトのバックエンドエージェントサーバー

# ローカルDB構築

./init_local_db.sh

# 起動方法

./start_server.sh


# 備考

## LiteLLM

OpenAI Agentだけで、LiteLLMを利用しない場合、OpenAIはデフォルトでResponse APIを利用しますが、

OpenAI Agent経由でLiteLLMを使ったら、Chat Completion APIしか使えないです。

[ここ](https://docs.litellm.ai/docs/providers/openai/responses_api)を見ると、OpenAI Agentを使わず、直接にLiteLLMを利用する場合、OpenAIのResponse APIが使えそうです。

### 定義済みモデル

1. bedrock/anthropic.claude-3-5-sonnet-20240620-v1
2. openai/gpt-4.1

定義詳細は「src/aica_agent/config.yml」参照

### モデルの追加

1. 「src/aica_agent/config.yml」にモデルの定義を追加
2. 「src/aica_agent/repositories/llm_repo.py」の「LLMModel」にモデルの定義を追加
3. 「src/aica_agent/repositories/llm_repo.py」の「active_agent()」にモデル選択分岐を追加

### モデル定義の変更

「src/aica_agent/config.yml」のmodel_listの中身を変更するだけです。

### 利用モデルの指定

リクエストパラメータで直接指定を避けるため、「src/aica_agent/endpoints.py」の「handle_chat_session」をいじって、サーバ再起動が必要となります。

### 備考

Bedrockを利用するには、.env.localに追加で
```
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=
AWS_REGION_NAME=ap-northeast-1
```
の定義が必要です。
