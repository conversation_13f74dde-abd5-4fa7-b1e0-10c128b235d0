#!/bin/bash

DB_IMAGE="pgvector/pgvector:pg16"
DB_CONTAINER_NAME="pgvector"
PROJECT_NAME="ai-ca"

# Filter for the container by both label and name
container_id=$(docker ps -a \
  --filter "label=com.docker.compose.project=${PROJECT_NAME}" \
  --filter "name=^${DB_CONTAINER_NAME}$" \
  --format "{{.ID}}")

# Function to create the container
create_container() {
  echo "Creating container '$DB_CONTAINER_NAME' under project '$PROJECT_NAME'..."
  docker run -d \
    --name "${DB_CONTAINER_NAME}" \
    -e POSTGRES_PASSWORD=postgres \
    -p 5432:5432 \
    --label "com.docker.compose.project=${PROJECT_NAME}" \
    --network ai-ca_default \
    $DB_IMAGE

  if [ $? -eq 0 ]; then
    echo "Container '$DB_CONTAINER_NAME' created successfully."
  else
    echo "Failed to create container '$DB_CONTAINER_NAME'."
    exit 1
  fi
}

if [ -z "$container_id" ]; then
  # Container does not exist at all
  create_container
else
  # Container exists, check if it's running or stopped
  status=$(docker inspect -f '{{.State.Status}}' "$container_id" 2>/dev/null)

  case "$status" in
    running)
      echo "Container '$DB_CONTAINER_NAME' is already running."
      ;;
    exited|created)
      echo "Container '$DB_CONTAINER_NAME' exists but is stopped. Starting it..."
      docker start "$container_id"
      ;;
    *)
      echo "Container '$DB_CONTAINER_NAME' is in status '$status'."
      echo "Recreating container..."
      docker rm -f "$container_id"
      create_container
      ;;
  esac
fi

echo "Waiting for PostgreSQL to be ready..."
while ! docker logs "${DB_CONTAINER_NAME}" 2>&1 | grep -q "database system is ready to accept connections"; do
   sleep 2
done
echo "PostgreSQL is ready."

echo "Creating extension 'vector'..."
docker exec -i "${DB_CONTAINER_NAME}" \
  psql -U postgres -d postgres -tAc "CREATE EXTENSION vector;"

# Function: Check if a table exists in the public schema
check_table() {
  local table=$1
  docker exec -i "${DB_CONTAINER_NAME}" \
    psql -U postgres -d postgres -tAc "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');"
}

# Function: Check if the function update_timestamp_column() exists in the public schema
check_function() {
  docker exec -i "${DB_CONTAINER_NAME}" \
    psql -U postgres -d postgres -tAc "SELECT EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE p.proname = 'update_timestamp_column' AND n.nspname = 'public');"
}

# Function: Run a SQL file inside the container
run_sql_file() {
  local sql_file=$1
  if [ -f "$sql_file" ]; then
    echo "Executing SQL file: $sql_file"
    cat "$sql_file" | docker exec -i "${DB_CONTAINER_NAME}" psql -U postgres -d postgres
    if [ $? -ne 0 ]; then
      echo "Error executing $sql_file"
      exit 1
    fi
  else
    echo "SQL file $sql_file not found! Skipping..."
  fi
}

# Function: Ensure that a table exists. If not, check and create the function update_timestamp_column(),
# and then run the provided SQL file to create/import the table.
ensure_table_exists() {
  local table_name="$1"
  local sql_file="$2"

  echo "Checking for table public.${table_name}..."
  local table_exists=$(check_table "${table_name}")
  echo "Table public.${table_name} exists: ${table_exists}"
  if [ "${table_exists}" != "t" ]; then
    echo "Table public.${table_name} does not exist."
    local func_exists=$(check_function)
    echo "Function public.update_timestamp_column exists: ${func_exists}"
    if [ "${func_exists}" != "t" ]; then
      echo "Function public.update_timestamp_column() does not exist. Creating it..."
      run_sql_file "db_migrations/sql/001_trigger.sql"
    fi
    echo "Creating and importing table public.${table_name} using ${sql_file}..."
    run_sql_file "db_migrations/sql/${sql_file}"
    run_sql_file "db_migrations/data/${sql_file}"
  else
    echo "Table public.${table_name} already exists. Skipping creation."
  fi
}

# Main script logic: Dynamically process all SQL files in db_migrations/sql
for sql_file in db_migrations/sql/*.sql; do
  # Extract the table name by removing the numeric prefix and `.sql` extension
  table_name=$(basename "$sql_file" .sql | sed 's/^[0-9]*_//')
  
  # Ensure the table exists and process the SQL file
  ensure_table_exists "$table_name" "$(basename "$sql_file")"
done

echo "Database checks and updates completed."